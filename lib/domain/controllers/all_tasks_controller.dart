import 'package:family_management/app_service.dart';
import 'package:family_management/domain/models/task.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class AllTasksController extends GetxController {
  final appService = Get.find<AppService>();
  final respository = gt<TaskRepository>();

  late String? status;
  final RxBool _loading = false.obs;
  final RxList<Task> tasks = <Task>[].obs;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    status = Get.arguments?['status'];
    fetchTasks();
  }

  fetchTasks() async {
    _loading.value = true;
    final user = appService.user;
    final result = await respository.getTasks(
        familyId: user.families.first.id!, status: status);
    result.fold((l) => Utils.showToast(l.message), (r) {
      tasks.value = r;
      tasks.refresh();
    });
    _loading.value = false;
  }
}
