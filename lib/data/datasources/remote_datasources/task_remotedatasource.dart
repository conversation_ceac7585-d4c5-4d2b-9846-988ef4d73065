import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class TaskRemoteDataSource extends BaseRemotedatasource {
  TaskRemoteDataSource({required super.dio});

  Future<Response> tasks(
      {required int familyId,
      int page = 1,
      String? status,
      String? search}) async {
    String url = 'tasks?family_id=$familyId&page=$page';
    if (status != null) {
      url += '&status=$status';
    }
    if (search != null && search.isNotEmpty) {
      url += '&search=$search';
    }
    return await get(endpoint: url);
  }

  Future<Response> storeOrUpdate({
    int? id,
    required int familyId,
    required String title,
    String? description,
    required String priority,
    String? dueDate,
    required List<int> assignees,
    required int type,
    String? recurringPattern,
    String? recurringInterval,
  }) async {
    Map<String, dynamic> data = {
      'family_id': familyId,
      'title': title,
      if (description != null) 'description': description,
      'priority': priority,
      if (dueDate != null) 'due_date': dueDate,
      'assignees': assignees,
      'type': type,
      if (type == 2) ...{
        'recurring_pattern': recurringPattern,
        'recurring_interval': recurringInterval,
      },
    };
    String url = 'tasks';
    if (id != null) {
      url = 'tasks/$id';
    }
    return await post(endpoint: url, data: data);
  }

  Future<Response> updateTask({
    required int id,
    String? title,
    String? description,
    String? priority,
    String? dueDate,
    String? status,
  }) async {
    Map<String, dynamic> data = {
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (priority != null) 'priority': priority,
      if (dueDate != null) 'due_date': dueDate,
      if (status != null) 'status': status,
    };

    FormData formData = FormData.fromMap(data);
    return await put(endpoint: 'tasks/$id', data: formData);
  }

  Future<Response> addSubtask({
    required int taskId,
    required String content,
    required String priority,
  }) async {
    return await post(
        endpoint: 'subTasks',
        data: {'task_id': taskId, 'content': content, 'priority': priority});
  }

  Future<Response> deleteSubtask(int subtaskId) async {
    return await delete(endpoint: 'subTasks/$subtaskId');
  }

  Future<Response> editSubtask(int id,
      {String? content, String? status}) async {
    Map data = {};
    if (content != null && content.isNotEmpty) data['content'] = content;
    if (status != null) data['status'] = status;
    return put(endpoint: 'subTasks/$id', data: data);
  }
}
