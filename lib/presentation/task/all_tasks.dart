import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/all_tasks_controller.dart';
import 'package:family_management/presentation/chat/widgets/search_box.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/task_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AllTasksView extends GetView<AllTasksController> {
  const AllTasksView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            appBar: mainAppBar(
                context: context,
                withBackButton: true,
                withDrawer: false,
                title: 'Tasks'.tr),
            body: controller.loading
                ? const Center(child: CircularProgressIndicator())
                : Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        const SearchBox(),
                        const SizedBox(height: 20),
                        Expanded(
                          child: ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              itemCount: controller.tasks.length,
                              itemBuilder: (context, index) {
                                final item = controller.tasks[index];
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    TaskCard(
                                      task: item,
                                      onPressed: (_) => Get.toNamed(
                                          AppRoutes.taskDetails,
                                          arguments: item),
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    )
                                  ],
                                );
                              }),
                        )
                      ],
                    ),
                  ),
            floatingActionButton: FloatingActionButton(
              backgroundColor: Theme.of(context).primaryColor,
              elevation: 4,
              onPressed: () => Get.toNamed(AppRoutes.addTask),
              child: const Icon(
                Icons.add,
                color: Colors.white,
              ),
            ),
          ),
        ));
  }
}
